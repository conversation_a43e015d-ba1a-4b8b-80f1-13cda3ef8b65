#pragma once

#include "../Common/Types.h"
#include <string>
#include <vector>

namespace MirServer {
namespace Protocol {

// 网络协议类型定义（完全对应delphi原版grobal2.pas）
enum PacketType : WORD {
    // =============== 客户端到服务器的协议（CM_开头）===============

    // 基础连接协议
    CM_PROTOCOL = 2000,           // 协议版本检查
    CM_IDPASSWORD = 2001,         // 登录密码
    CM_ADDNEWUSER = 2002,         // 添加新用户
    CM_CHANGEPASSWORD = 2003,     // 修改密码
    CM_UPDATEUSER = 2004,         // 更新用户信息
    CM_GETBACKPASSWORD = 2005,    // 找回密码

    // 角色管理协议
    CM_QUERYCHR = 100,            // 查询角色
    CM_NEWCHR = 101,              // 创建角色
    CM_DELCHR = 102,              // 删除角色
    CM_SELCHR = 103,              // 选择角色
    CM_SELECTSERVER = 104,        // 选择服务器

    // 数据库服务器协议（DB_开头）
    DB_LOADHUMANRCD = 200,        // 加载人物记录
    DB_SAVEHUMANRCD = 201,        // 保存人物记录
    DB_SAVEHUMANRCDEX = 202,      // 扩展保存人物记录
    DB_QUERYCHR = 203,            // 查询角色
    DB_NEWCHR = 204,              // 新建角色
    DB_DELCHR = 205,              // 删除角色
    DB_LOADHERORCD = 206,         // 加载英雄记录
    DB_SAVEHERORCD = 207,         // 保存英雄记录
    DB_DELHERORCD = 208,          // 删除英雄记录

    // 数据库服务器响应协议（DBR_开头）
    DBR_LOADHUMANRCD = 250,       // 加载人物记录响应
    DBR_SAVEHUMANRCD = 251,       // 保存人物记录响应
    DBR_QUERYCHR = 252,           // 查询角色响应
    DBR_NEWCHR = 253,             // 新建角色响应
    DBR_DELCHR = 254,             // 删除角色响应
    DBR_FAIL = 255,               // 操作失败

    // 游戏内基础动作协议
    CM_QUERYUSERNAME = 80,        // 查询用户名称
    CM_QUERYBAGITEMS = 81,        // 查询背包物品
    CM_DROPITEM = 1000,           // 扔物品
    CM_PICKUP = 1001,             // 拾取物品
    CM_USEBAGITEM = 1003,         // 使用背包物品
    CM_TAKEONITEM = 1004,         // 佩戴物品
    CM_TAKEOFFITEM = 1005,        // 卸下物品
    CM_EAT = 1006,                // 吃东西
    CM_BUTCH = 1007,              // 挖肉
    CM_MAGICKEYCHANGE = 1008,     // 改变魔法快捷键
    CM_CLICKNPC = 1010,           // 点击NPC
    CM_MERCHANTDLGSELECT = 1011,  // 商人对话选择
    CM_MERCHANTQUERYSELLPRICE = 1012, // 商人查询卖价
    CM_USERSELLITEM = 1013,       // 用户卖物品
    CM_USERBUYITEM = 1014,        // 用户买物品
    CM_USERGETDETAILITEM = 1015,  // 用户获取物品详情
    CM_DROPGOLD = 1016,           // 扔金币
    CM_LOGINNOTICEOK = 1018,      // 登录游戏公告确认按钮
    CM_GROUPMODE = 1019,          // 组队模式
    CM_CREATEGROUP = 1020,        // 创建组队
    CM_ADDGROUPMEMBER = 1021,     // 添加组队成员
    CM_DELGROUPMEMBER = 1022,     // 删除组队成员
    CM_GROUPCHAT = 1035,          // 组队聊天
    CM_GROUPTELEPORT = 1036,      // 组队传送
    CM_GROUPEXPMODE = 1037,       // 设置经验分配模式
    CM_USERREPAIRITEM = 1023,     // 修理物品
    CM_MERCHANTQUERYREPAIRCOST = 1024, // 查询修理价格

    // 交易系统协议
    CM_DEALTRY = 1025,            // 交易开始
    CM_DEALADDITEM = 1026,        // 交易添加物品
    CM_DEALDELITEM = 1027,        // 交易删除物品
    CM_DEALCANCEL = 1028,         // 交易取消
    CM_DEALCHGGOLD = 1029,        // 交易改变金币
    CM_DEALEND = 1030,            // 交易结束

    // 仓库系统协议
    CM_USERSTORAGEITEM = 1031,    // 用户存储物品
    CM_USERTAKEBACKSTORAGEITEM = 1032, // 从仓库取回物品
    CM_WANTMINIMAP = 1033,        // 请求小地图
    CM_USERMAKEDRUGITEM = 1034,   // 制造药品物品

    // 移动和战斗协议
    CM_WALK = 3000,               // 行走
    CM_RUN = 3001,                // 跑步
    CM_HIT = 3002,                // 攻击
    CM_HEAVYHIT = 3003,           // 重击
    CM_BIGHIT = 3004,             // 大击
    CM_SPELL = 3005,              // 魔法
    CM_POWERHIT = 3006,           // 烈火剑法
    CM_LONGHIT = 3007,            // 刺杀剑术
    CM_WIDEHIT = 3008,            // 半月弯刀
    CM_FIREHIT = 3009,            // 烈火剑法
    CM_4009 = 4009,               // 未知协议
    CM_4010 = 4010,               // 未知协议
    CM_4011 = 4011,               // 未知协议
    CM_4012 = 4012,               // 未知协议

    CM_SAY = 3030,                // 说话
    CM_WHISPER = 3031,            // 悄悄话
    CM_GM = 3032,                 // GM命令
    CM_TURN = 3033,               // 转向
    CM_LOGINOUT = 3034,           // 登出
    CM_LOGINGAME = 3035,          // 登录游戏
    CM_ALIVE = 3036,              // 复活

    // 高级战斗动作协议
    CM_RUSH = 3006,               // 冲锋
    CM_RUSHKUNG = 3007,           // 野蛮冲撞
    CM_BACKSTEP = 3009,           // 后退步法
    CM_DIGUP = 3020,              // 挖取
    CM_DIGDOWN = 3021,            // 挖掘
    CM_FLYAXE = 3022,             // 飞斧
    CM_LIGHTING = 3023,           // 雷电
    CM_SUBABILITY = 3024,         // 降低能力
    CM_HORSERUN = 3035,           // 骑马跑
    CM_CRSHIT = 3036,             // 连击
    CM_TWINHIT = 3038,            // 双击
    CM_RIDE = 3031,               // 骑马

    // =============== 服务器到客户端的协议（SM_开头）===============

    // 基础响应协议
    SM_OUTOFCONNECTION = 1,       // 连接断开
    SM_PASSOK_SELECTSERVER = 3,   // 密码正确，选择服务器
    SM_SELECTSERVER_OK = 530,     // 选择服务器成功

    // 登录相关响应
    SM_CERTIFICATION_SUCCESS = 500, // 认证成功
    SM_CERTIFICATION_FAIL = 501,   // 认证失败
    SM_ID_NOTFOUND = 502,         // ID未找到
    SM_PASSWD_FAIL = 503,         // 密码错误
    SM_NEWID_SUCCESS = 504,       // 新ID成功
    SM_NEWID_FAIL = 505,          // 新ID失败
    SM_CHGPASSWD_SUCCESS = 506,   // 修改密码成功
    SM_CHGPASSWD_FAIL = 507,      // 修改密码失败
    SM_GETBACKPASSWD_SUCCESS = 508, // 找回密码成功
    SM_GETBACKPASSWD_FAIL = 509,  // 找回密码失败

    // 角色管理响应
    SM_QUERYCHR = 520,            // 查询角色响应
    SM_NEWCHR_SUCCESS = 521,      // 创建角色成功
    SM_NEWCHR_FAIL = 522,         // 创建角色失败
    SM_DELCHR_SUCCESS = 523,      // 删除角色成功
    SM_DELCHR_FAIL = 524,         // 删除角色失败
    SM_STARTPLAY = 525,           // 开始游戏
    SM_STARTFAIL = 526,           // 开始游戏失败
    SM_QUERYCHR_FAIL = 527,       // 查询角色失败
    SM_OUTOFCONNECTION2 = 528,    // 连接断开2

    // 游戏内状态响应
    SM_MAPCHANGED = 601,          // 地图改变
    SM_LOGON = 602,               // 登录
    SM_MAPDESCRIPTION = 603,      // 地图描述
    SM_ABILITY = 604,             // 能力值
    SM_HEALTHSPELLCHANGED = 605,  // 生命魔法值改变
    SM_DAYCHANGING = 614,         // 昼夜变化
    SM_LOGINSTATUS = 615,         // 登录状态
    SM_NEWMAP = 616,              // 新地图
    SM_RECONNECT = 617,           // 重连
    SM_GHOST = 618,               // 幽灵状态
    SM_SHOWEVENT = 619,           // 显示事件
    SM_HIDEEVENT = 620,           // 隐藏事件
    SM_SPACEMOVE_HIDE = 621,      // 隐身移动
    SM_SPACEMOVE_SHOW = 622,      // 显示移动
    SM_RECONNECT2 = 623,          // 重连2
    SM_MOVEMODE_CHANGED = 624,    // 移动模式改变
    SM_SPACEMOVE_HIDE2 = 1041,    // 隐身移动2
    SM_SPACEMOVE_SHOW2 = 1043,    // 显示移动2
    SM_MOVEFAIL = 1045,           // 移动失败

    // 移动和动作响应
    SM_WALK = 800,                // 行走
    SM_RUN = 801,                 // 跑步
    SM_HIT = 802,                 // 攻击
    SM_HEAVYHIT = 803,            // 重击
    SM_BIGHIT = 804,              // 大击
    SM_SPELL = 805,               // 魔法
    SM_POWERHIT = 806,            // 烈火剑法
    SM_LONGHIT = 807,             // 刺杀剑术
    SM_WIDEHIT = 808,             // 半月弯刀
    SM_FIREHIT = 809,             // 烈火剑法
    SM_SAY = 830,                 // 说话
    SM_TURN = 833,                // 转向

    // 物品和背包管理响应
    SM_ADDITEM = 701,             // 添加物品
    SM_BAGITEMS = 702,            // 背包物品
    SM_DELITEM = 703,             // 删除物品
    SM_UPDATEITEM = 704,          // 更新物品
    SM_ADDMAGIC = 705,            // 添加魔法
    SM_SENDMYMAGIC = 706,         // 发送我的魔法
    SM_DELMAGIC = 707,            // 删除魔法
    SM_MAGIC_LVEXP = 708,         // 魔法等级经验
    SM_DURACHANGE = 709,          // 持久度改变
    SM_MERCHANTSAY = 710,         // 商人说话
    SM_MERCHANTDLG = 711,         // 商人对话
    SM_SENDGOODSLIST = 712,       // 发送商品列表
    SM_SENDUSERSELL = 713,        // 发送用户出售
    SM_SENDBUYPRICE = 714,        // 发送购买价格
    SM_USERSELLITEM_OK = 715,     // 用户卖物品成功
    SM_USERSELLITEM_FAIL = 716,   // 用户卖物品失败
    SM_BUYITEM_SUCCESS = 717,     // 购买物品成功
    SM_BUYITEM_FAIL = 718,        // 购买物品失败
    SM_SENDDETAILGOODSLIST = 719, // 发送详细商品列表
    SM_GOLDCHANGED = 720,         // 金币改变
    SM_CHANGELIGHT = 721,         // 改变光源
    SM_LAMPCHANGEDURA = 722,      // 灯具耐久改变
    SM_CHANGENAMECOLOR = 723,     // 改变名称颜色
    SM_TAKEONITEM = 724,          // 穿戴物品
    SM_TAKEOFFITEM = 725,         // 卸下物品

    // 修理系统响应
    SM_SENDREPAIRCOST = 2080,     // 发送修理费用
    SM_USERREPAIRITEM_OK = 2081,  // 修理物品成功
    SM_USERREPAIRITEM_FAIL = 2082, // 修理物品失败

    // 仓库系统响应
    SM_STORAGE_OK = 2083,         // 仓库操作成功
    SM_STORAGE_FULL = 2084,       // 仓库已满
    SM_STORAGE_FAIL = 2085,       // 仓库操作失败
    SM_SAVEITEMLIST = 2086,       // 保存物品列表
    SM_TAKEBACKSTORAGEITEM_OK = 2087,     // 取回物品成功
    SM_TAKEBACKSTORAGEITEM_FAIL = 2088,   // 取回物品失败
    SM_TAKEBACKSTORAGEITEM_FULLBAG = 2089, // 取回物品背包满
    SM_STORAGEPASSWORD_OK = 2090,         // 仓库密码正确
    SM_STORAGEPASSWORD_FAIL = 2091,       // 仓库密码错误
    SM_MAKEDRUG_SUCCESS = 2092,           // 制药成功
    SM_MAKEDRUG_FAIL = 2093,              // 制药失败

    // 交易系统响应
    SM_DEALTRY_FAIL = 2108,       // 交易开始失败
    SM_DEALMENU = 2109,           // 交易菜单
    SM_DEALCANCEL = 2110,         // 交易取消
    SM_DEALADDITEM_OK = 2111,     // 添加物品成功
    SM_DEALADDITEM_FAIL = 2112,   // 添加物品失败
    SM_DEALDELITEM_OK = 2113,     // 删除物品成功
    SM_DEALDELITEM_FAIL = 2114,   // 删除物品失败
    SM_DEALREMOTEADDITEM = 682,   // 远程交易添加物品
    SM_DEALREMOTEDELITEM = 683,   // 远程交易删除物品
    SM_DEALCHGGOLD_OK = 2117,     // 改变金币成功
    SM_DEALCHGGOLD_FAIL = 2118,   // 改变金币失败
    SM_DEALREMOTECHGGOLD = 2119,  // 远程改变金币
    SM_DEALSUCCESS = 2120,        // 交易成功

    // 小地图响应
    SM_READMINIMAP_OK = 710,      // 读取小地图成功
    SM_READMINIMAP_FAIL = 711,    // 读取小地图失败
    SM_SENDMINIMAP = 712,         // 发送小地图数据

    // 任务系统响应
    SM_QUESTACCEPT = 2200,        // 任务接受
    SM_QUESTUPDATE = 2201,        // 任务更新
    SM_QUESTCOMPLETE = 2202,      // 任务完成
    SM_QUESTABANDON = 2203,       // 任务放弃

    // 组队系统响应
    SM_GROUPMEMBERS = 2300,       // 组队成员列表
    SM_GROUPCANCEL = 2301,        // 组队取消/解散
    SM_GROUPINVITE = 2302,        // 组队邀请

    // 行会系统协议
    CM_OPENGUILDDLG = 2400,       // 打开行会对话框
    CM_GUILDHOME = 2401,          // 行会主页
    CM_GUILDMEMBERLIST = 2402,    // 行会成员列表
    CM_GUILDADDMEMBER = 2403,     // 添加行会成员
    CM_GUILDDELMEMBER = 2404,     // 删除行会成员
    CM_GUILDUPDATENOTICE = 2405,  // 更新行会公告
    CM_GUILDUPDATERANKINFO = 2406, // 更新行会职位信息
    CM_GUILDMAKEALLY = 2407,      // 行会结盟
    CM_GUILDBREAKALLY = 2408,     // 行会解除联盟
    CM_GUILDWAR = 2409,           // 行会宣战
    CM_GUILDPEACE = 2410,         // 行会停战
    CM_GUILDMSG = 2411,           // 行会聊天

    // 行会系统响应
    SM_OPENGUILDDLG = 2450,       // 打开行会对话框响应
    SM_OPENGUILDDLG_FAIL = 2451,  // 打开行会对话框失败
    SM_SENDGUILDMEMBERLIST = 2452, // 发送行会成员列表
    SM_GUILDADDMEMBER_OK = 2453,  // 添加行会成员成功
    SM_GUILDADDMEMBER_FAIL = 2454, // 添加行会成员失败
    SM_GUILDDELMEMBER_OK = 2455,  // 删除行会成员成功
    SM_GUILDDELMEMBER_FAIL = 2456, // 删除行会成员失败
    SM_GUILDRANKUPDATE_FAIL = 2457, // 行会职位更新失败
    SM_GUILDMAKEALLY_OK = 2458,   // 行会结盟成功
    SM_GUILDMAKEALLY_FAIL = 2459, // 行会结盟失败
    SM_GUILDBREAKALLY_OK = 2460,  // 行会解除联盟成功
    SM_GUILDBREAKALLY_FAIL = 2461, // 行会解除联盟失败
    SM_GUILDWAR_OK = 2462,        // 行会宣战成功
    SM_GUILDWAR_FAIL = 2463,      // 行会宣战失败
    SM_GUILDPEACE_OK = 2464,      // 行会停战成功
    SM_GUILDPEACE_FAIL = 2465,    // 行会停战失败
    SM_GUILDMESSAGE = 2466,       // 行会消息
    SM_CHANGEGUILDNAME = 2467,    // 改变行会名称显示

    // 交易系统扩展
    SM_DEALADDITEM = 2111,        // 交易添加物品（重新定义以避免冲突）
    SM_DEALEND = 2120,            // 交易结束

    // 服务器配置响应
    SM_SERVERCONFIG = 5007,       // 服务器配置
    SM_GAMEGOLDNAME = 5008,       // 游戏金币名称
    SM_PASSWORD = 5009,           // 密码相关

    // 特殊效果响应
    SM_RIDEHORSE = 1300,          // 骑马
    SM_MONSTERSAY = 1501,         // 怪物说话
    SM_ALIVE = 263,               // 复活
    SM_INSTANCEHEALGUAGE = 314,   // 实例治疗量表
    SM_BREAKWEAPON = 315,         // 武器破碎
    SM_HIDE = 1224,               // 隐身
    SM_NOWDEATH = 34,             // 当前死亡
    SM_41 = 36,                   // 未知状态
    SM_FEATURECHANGED = 41,       // 特征改变
    SM_USERNAME = 42,             // 用户名
    SM_MYSTATUS = 766,            // 我的状态

    // 高级战斗响应
    SM_RUSHKUNG = 7,              // 野蛮冲撞
    SM_RUSH = 6,                  // 冲锋
    SM_BACKSTEP = 9,              // 后退步法
    SM_HORSERUN = 5010,           // 骑马跑
    SM_CRSHIT = 25,               // 连击
    SM_TWINHIT = 26,              // 双击
    SM_DIGUP = 20,                // 挖取
    SM_DIGDOWN = 21,              // 挖掘
    SM_FLYAXE = 22,               // 飞斧
    SM_LIGHTING = 23,             // 雷电

    // 战斗系统消息常量
    RM_STRUCK = 10101,            // 受到攻击
    RM_10101 = 10101,             // 攻击消息子类型
    RM_DEATH = 10102,             // 死亡
    RM_SKELETON = 10103,          // 骨架
    RM_DISAPPEAR = 10104,         // 消失
    RM_DURACHANGE = 10125,        // 耐久度变化
    RM_ABILITY = 10051,           // 属性变化
    RM_SUBABILITY = 10302,        // 子属性变化
    RM_SYSMESSAGE = 10100,        // 系统消息
    RM_HIT = 10030,               // 攻击动作
    RM_TURN = 10031,              // 转向
    RM_WALK = 10032,              // 行走
    RM_RUN = 10033,               // 跑步
    RM_HEAVYHIT = 10034,          // 重击
    RM_BIGHIT = 10035,            // 大击
    RM_SPELL = 10036,             // 魔法
    RM_POWERHIT = 10037,          // 烈火剑法
    RM_LONGHIT = 10038,           // 刺杀剑术
    RM_WIDEHIT = 10039,           // 半月弯刀
    RM_FIREHIT = 10040,           // 烈火剑法
    RM_MAGIC_LVEXP = 10123,       // 魔法等级经验

    // 状态变化消息
    RM_CHARSTATUSCHANGED = 10139, // 角色状态改变
    RM_HEAR = 10105,              // 听到消息
    SM_CHARSTATUSCHANGED = 10139, // 角色状态改变
    SM_SUBABILITY = 10302,        // 子属性
    SM_TAKEON_OK = 724,           // 穿戴成功
    SM_TAKEON_FAIL = 725,         // 穿戴失败
    SM_TAKEOFF_OK = 726,          // 卸下成功
    SM_TAKEOFF_FAIL = 727,        // 卸下失败
    SM_EAT_OK = 728,              // 使用物品成功
    SM_EAT_FAIL = 729,            // 使用物品失败

    // 状态效果协议
    SM_SKILL_SWITCH = 730,        // 技能开关
    SM_DEFENCEUP = 731,           // 防御提升
    SM_MAGDEFENCEUP = 732,        // 魔防提升
    SM_MAGBUBBLEDEFENCEUP = 733,  // 魔法盾
    SM_HOLYSEIZE = 734,           // 神圣战甲术
    SM_CRAZYMODE = 735,           // 疯狂模式
    SM_OPENHEALTH = 736,          // 显示血量
    SM_POISON = 737,              // 中毒

    // 魔法效果协议
    RM_MAGICFIRE = 10200,         // 火球术效果
    RM_LIGHTING = 10201,          // 雷电术效果
};

// 数据包头结构
#pragma pack(push, 1)
struct PacketHeader {
    WORD length;                  // 数据包长度
    WORD packetType;              // 数据包类型
    WORD sequence;                // 序列号（可选）

    PacketHeader() : length(0), packetType(0), sequence(0) {}
    PacketHeader(WORD len, WORD type, WORD seq = 0)
        : length(len), packetType(type), sequence(seq) {}
};

// 登录请求数据包
struct LoginRequestPacket {
    PacketHeader header;
    char account[32];             // 账号
    char password[32];            // 密码
    BYTE clientVersion;           // 客户端版本
    BYTE reserved[3];             // 保留字段

    LoginRequestPacket() {
        header.packetType = CM_IDPASSWORD;
        header.length = sizeof(LoginRequestPacket);
        memset(account, 0, sizeof(account));
        memset(password, 0, sizeof(password));
        clientVersion = 0;
        memset(reserved, 0, sizeof(reserved));
    }
};

// 角色信息数据包
struct CharacterInfoPacket {
    PacketHeader header;
    char charName[32];            // 角色名
    BYTE job;                     // 职业
    BYTE gender;                  // 性别
    BYTE level;                   // 等级
    BYTE reserved;                // 保留

    CharacterInfoPacket() {
        header.packetType = SM_QUERYCHR;
        header.length = sizeof(CharacterInfoPacket);
        memset(charName, 0, sizeof(charName));
        job = 0;
        gender = 0;
        level = 1;
        reserved = 0;
    }
};

// 移动数据包
struct MovePacket {
    PacketHeader header;
    WORD x;                       // X坐标
    WORD y;                       // Y坐标
    BYTE direction;               // 方向
    BYTE reserved[3];             // 保留

    MovePacket() {
        header.length = sizeof(MovePacket);
        x = 0;
        y = 0;
        direction = 0;
        memset(reserved, 0, sizeof(reserved));
    }
};

// 说话数据包
struct SayPacket {
    PacketHeader header;
    BYTE sayType;                 // 说话类型（普通/组队/行会等）
    BYTE color;                   // 颜色
    WORD textLength;              // 文本长度
    // 紧跟着文本内容

    SayPacket() {
        header.packetType = CM_SAY;
        header.length = sizeof(SayPacket);
        sayType = 0;
        color = 0;
        textLength = 0;
    }
};

// 物品数据包
struct ItemPacket {
    PacketHeader header;
    WORD makeIndex;               // 制造索引
    WORD itemIndex;               // 物品索引
    WORD dura;                    // 持久度
    WORD duraMax;                 // 最大持久度
    BYTE nameLength;              // 名称长度
    // 紧跟着物品名称
    BYTE btValue[14];             // 附加属性

    ItemPacket() {
        header.length = sizeof(ItemPacket);
        makeIndex = 0;
        itemIndex = 0;
        dura = 0;
        duraMax = 0;
        nameLength = 0;
        memset(btValue, 0, sizeof(btValue));
    }
};

// 默认消息结构（对应delphi的TDefaultMessage）
#pragma pack(push, 1)
struct DefaultMessage {
    int32_t nRecog;               // 识别码
    WORD wIdent;                  // 消息标识
    WORD wParam;                  // 参数
    WORD wTag;                    // 标记
    WORD wSeries;                 // 序列

    DefaultMessage() : nRecog(0), wIdent(0), wParam(0), wTag(0), wSeries(0) {}
    DefaultMessage(WORD ident, int32_t recog = 0, WORD param = 0, WORD tag = 0, WORD series = 0)
        : nRecog(recog), wIdent(ident), wParam(param), wTag(tag), wSeries(series) {}
};

// 查询角色信息结构
struct QueryChr {
    char sName[15];               // 角色名称
    BYTE btJob;                   // 职业
    BYTE btHair;                  // 发型
    BYTE btLevel;                 // 等级
    BYTE btSex;                   // 性别

    QueryChr() {
        memset(sName, 0, sizeof(sName));
        btJob = 0;
        btHair = 0;
        btLevel = 1;
        btSex = 0;
    }
};
#pragma pack(pop)

// 创建默认消息的辅助函数
inline DefaultMessage MakeDefaultMsg(uint16_t wIdent, int nRecog = 0, uint16_t wParam = 0,
                                   uint16_t wTag = 0, uint16_t wSeries = 0) {
    DefaultMessage msg;
    msg.nRecog = nRecog;
    msg.wIdent = wIdent;
    msg.wParam = wParam;
    msg.wTag = wTag;
    msg.wSeries = wSeries;
    return msg;
}

// 工具函数声明
std::string GetPacketTypeName(PacketType type);
bool IsClientPacket(PacketType type);
bool IsServerPacket(PacketType type);
bool RequiresLogin(PacketType type);
bool IsGamePlayPacket(PacketType type);

} // namespace Protocol
} // namespace MirServer