#pragma once

#include "BaseObject.h"
#include "../Common/Types.h"
#include <deque>

namespace MirServer {

// 玩家对象类（对应delphi的TPlayObject）
class PlayObject : public BaseObject {
public:
    PlayObject();
    virtual ~PlayObject();

    // 重写基类方法
    virtual ObjectType GetObjectType() const override { return ObjectType::HUMAN; }
    virtual void Initialize() override;
    virtual void Finalize() override;
    virtual void Run() override;
    virtual bool Walk(DirectionType dir) override;
    virtual void SendMessage(const std::string& msg, BYTE color = 0) override;
    virtual void SendDefMessage(WORD msgType, WORD recog = 0,
                              WORD param = 0, WORD tag = 0, WORD series = 0) override;

    // 角色基本信息
    const HumDataInfo& GetHumDataInfo() const { return m_humDataInfo; }
    void SetHumDataInfo(const HumDataInfo& info) { m_humDataInfo = info; }

    // 会话信息
    const SessionInfo& GetSessionInfo() const { return m_sessionInfo; }
    void SetSessionInfo(const SessionInfo& info) { m_sessionInfo = info; }

    // 属性相关
    const Ability& GetAbility() const { return m_humDataInfo.abil; }
    void SetAbility(const Ability& abil) { m_humDataInfo.abil = abil; }
    void RecalcAbility(); // 重新计算属性

    // 装备相关
    bool TakeOnItem(const UserItem& item, EquipPosition pos);
    bool TakeOffItem(EquipPosition pos, UserItem& outItem);
    const UserItem* GetEquipItem(EquipPosition pos) const;

    // 背包相关
    bool AddBagItem(const UserItem& item);
    bool DeleteBagItem(WORD makeIndex);
    bool UseBagItem(WORD makeIndex);
    const std::vector<UserItem>& GetBagItems() const { return m_humDataInfo.bagItems; }
    int GetBagItemCount() const { return static_cast<int>(m_humDataInfo.bagItems.size()); }
    bool IsBagFull() const { return GetBagItemCount() >= 40; } // 默认背包40格

    // 魔法相关
    bool AddMagic(const UserMagic& magic);
    bool DeleteMagic(WORD magicId);
    bool UseMagic(WORD magicId, BaseObject* target = nullptr);
    const std::vector<UserMagic>& GetMagics() const { return m_humDataInfo.magics; }

    // 金币相关
    DWORD GetGold() const { return m_humDataInfo.gold; }
    bool IncGold(DWORD amount);
    bool DecGold(DWORD amount);

    bool CheckLevelUp();

    // 组队相关
    bool IsGroupMember(const PlayObject* player) const;
    bool IsInGroup() const;
    bool IsGroupLeader() const;
    std::vector<std::shared_ptr<PlayObject>> GetGroupMembers() const;

    // 交易相关
    void SetDealPartner(PlayObject* partner) { m_dealPartner = partner; }
    PlayObject* GetDealPartner() const { return m_dealPartner; }
    bool IsDealActive() const { return m_dealPartner != nullptr; }
    void CancelDeal();

    // PK相关
    void SetAttackMode(AttackMode mode) { m_attackMode = mode; }
    AttackMode GetAttackMode() const { return m_attackMode; }
    void IncPKPoint(int value);
    DWORD GetPKPoint() const { return m_humDataInfo.pkPoint; }
    void SetLastAttacker(BaseObject* attacker) { m_lastAttacker = attacker; }

    // 重写战斗相关
    virtual bool IsAttackTarget(const BaseObject* target) const override;
    virtual bool IsProperTarget(const BaseObject* target) const override;
    virtual bool IsProperFriend(const BaseObject* target) const override;
    virtual void BeAttacked(BaseObject* attacker, int damage) override;

    // 重写技能相关的目标判断方法
    virtual bool IsProperTargetSKILL_54(const BaseObject* target) const override;
    virtual bool IsProperTargetSKILL_55(int level, const BaseObject* target) const override;
    virtual bool IsProperTargetSKILL_56(const BaseObject* target, int targetX, int targetY) const override;
    virtual bool IsProperTargetSKILL_57(const BaseObject* target) const override;
    virtual bool IsProperTargetSKILL_70(const BaseObject* target) const override;

    // 重写技能开关控制方法
    virtual void ThrustingOnOff(bool enable) override;
    virtual void HalfMoonOnOff(bool enable) override;
    virtual void SkillCrsOnOff(bool enable) override;
    virtual void Skill42OnOff(bool enable) override;
    virtual void Skill43OnOff(bool enable) override;

    // 重写高级战斗方法
    virtual bool RunTo(BYTE dir, bool flag, int destX, int destY) override;
    virtual bool AllowFireHitSkill() const override;

    // 辅助战斗方法
    void AttackNearTargets(int damage);
    int CalculateDamage(const BaseObject* target) const;
    DWORD GetAttackSpeed() const;
    void GetObjectsAtPosition(const Point& pos, std::vector<BaseObject*>& objects) const;

    // 客户端通信
    void SendMapInfo();
    void SendAbility();
    void SendBagItems();
    void SendMagics();
    void SendGoldChanged();
    void SendExpChanged();
    void SendLevelUp();
    void SendHealthChanged();
    void SendStorageItems(const std::vector<UserItem>& items, DWORD gold);

    // 聊天相关
    void Say(const std::string& msg);
    void Whisper(const std::string& targetName, const std::string& msg);
    void GroupMsg(const std::string& msg);
    void GuildMsg(const std::string& msg);

    // 行会相关
    bool IsGuildMember(const PlayObject* player) const;
    bool IsGuildAlly(const PlayObject* player) const;
    bool IsGuildEnemy(const PlayObject* player) const;
    const std::string& GetGuildName() const { return m_humDataInfo.guildName; }
    BYTE GetGuildRank() const { return m_humDataInfo.guildRank; }
    void SetGuildInfo(const std::string& guildName, BYTE rank);
    void ClearGuildInfo();

    // 登录/登出
    void LoginGame();
    void LogoutGame();
    void SaveData();
    void LoadData();

    // 复活相关
    virtual void Die() override;
    virtual void Revive() override;
    void ReviveAtHome(); // 回城复活

    // 重写物品掉落方法
    virtual void ScatterBagItems(BaseObject* itemCreator) override;
    virtual void DropUseItems(BaseObject* dropCreator) override;

    // NPC交互系统
    bool CanTalkToNPC(const BaseObject* npc) const;
    void TalkToNPC(BaseObject* npc);

    // 状态检查和更新
    void CheckStatusTimeOut();
    void CheckPoisonStatus();

    // 物品使用系统
    bool UseItem(WORD makeIndex);

    // 经验值和升级系统增强
    void GainExpFromKill(const BaseObject* target);
    DWORD CalculateExpGain(const BaseObject* target) const;
    void ShareExpWithGroup(DWORD totalExp);

    // 传送相关
    bool SpaceMove(const std::string& mapName, int x, int y);
    bool CanSpaceMove() const;

    // 仓库相关
    bool OpenStorage(const std::string& password);
    bool CloseStorage();
    bool StorageAddItem(const UserItem& item);
    bool StorageTakeItem(WORD makeIndex, UserItem& outItem);
    const std::vector<UserItem>& GetStorageItems() const { return m_humDataInfo.storageItems; }

    // 获取/设置基本属性
    JobType GetJob() const { return m_humDataInfo.job; }
    GenderType GetGender() const { return m_humDataInfo.gender; }
    WORD GetLevel() const { return m_humDataInfo.level; }
    const std::string& GetMapName() const { return m_humDataInfo.mapName; }
    bool IsOnline() const { return m_sessionInfo.isValid; }
    bool IsDead() const { return m_humDataInfo.isDead; }

    // 职业和性别设置
    void SetJob(JobType job) { m_humDataInfo.job = job; m_job = job; }
    void SetGender(GenderType gender) { m_humDataInfo.gender = gender; m_gender = gender; }
    void SetLevel(WORD level);

    // 金币
    void SetGold(DWORD gold);

    // 经验相关
    DWORD GetExp() const { return m_humDataInfo.abil.Exp; }
    void SetExp(DWORD exp) { m_humDataInfo.abil.Exp = exp; }
    void GainExp(DWORD exp);
    void GainExpDirect(DWORD exp); // 直接获得经验，不通过组队分享

    // 连接管理
    void SetConnectionId(uint32_t id) { m_connectionId = id; }
    uint32_t GetConnectionId() const { return m_connectionId; }
    DWORD GetLastActiveTime() const { return m_lastActiveTime; }
    void UpdateActiveTime() { m_lastActiveTime = GetCurrentTime(); }

    // 账号和IP信息
    const std::string& GetAccountName() const { return m_sessionInfo.accountName; }
    void SetAccountName(const std::string& accountName) { m_sessionInfo.accountName = accountName; }
    const std::string& GetIPAddress() const { return m_sessionInfo.ipAddress; }
    void SetIPAddress(const std::string& ipAddress) { m_sessionInfo.ipAddress = ipAddress; }

    // 网络通信
    void SendPacket(const std::vector<uint8_t>& packet);

protected:
    // 内部方法
    virtual void OnPositionChanged() override;
    virtual void OnDirectionChanged() override;
    virtual void OnStateChanged() override;
    virtual void OnHPChanged() override;

    void ProcessMessages(); // 处理客户端消息
    void UpdateViewMap();   // 更新视野地图

private:
    HumDataInfo m_humDataInfo;      // 角色数据
    SessionInfo m_sessionInfo;      // 会话信息

    // 交易信息
    PlayObject* m_dealPartner = nullptr;
    std::vector<UserItem> m_dealItems;
    DWORD m_dealGold = 0;
    bool m_dealLocked = false;

    // 战斗模式
    AttackMode m_attackMode = AttackMode::PEACE;

    // 战斗相关
    BaseObject* m_lastAttacker = nullptr;
    DWORD m_lastAttackTime = 0;
    DWORD m_lastMoveTime = 0;

    // 仓库状态
    bool m_storageOpened = false;

    // 消息队列
    struct Message {
        WORD msgType;
        WORD recog;
        WORD param;
        WORD tag;
        WORD series;
        std::string data;
    };
    std::deque<Message> m_messageQueue;

    // 视野缓存
    std::vector<BaseObject*> m_viewList;
    DWORD m_lastViewUpdateTime = 0;

    // 职业和性别设置
    JobType m_job;
    GenderType m_gender;

    // 连接管理
    uint32_t m_connectionId;
    DWORD m_lastActiveTime;
};

// 智能指针类型定义
using PlayObjectPtr = std::shared_ptr<PlayObject>;
using PlayObjectWeakPtr = std::weak_ptr<PlayObject>;

} // namespace MirServer