#pragma once

#include "../Common/Types.h"
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>

namespace MirServer {

// 前向声明
class Environment;
class BaseObject;

// 对象状态
enum class ObjectState : BYTE {
    NORMAL = 0,         // 正常
    DEAD = 1,           // 死亡
    GHOST = 2,          // 幽灵
    PARALYSIS = 3,      // 麻痹
    POISON = 4,         // 中毒
    HIDE = 5,           // 隐身
    STONE = 6           // 石化
};

// 攻击模式
enum class AttackMode : BYTE {
    PEACE = 0,          // 和平模式
    DEAR = 1,           // 夫妻模式
    MASTER = 2,         // 师徒模式
    GROUP = 3,          // 组队模式
    GUILD = 4,          // 行会模式
    REDNAME = 5,        // 善恶模式
    ALL = 6             // 全体模式
};

// 基础对象类（对应delphi的TBaseObject）
class BaseObject : public std::enable_shared_from_this<BaseObject> {
public:
    BaseObject();
    virtual ~BaseObject();

    // 基本属性
    virtual ObjectType GetObjectType() const = 0;
    virtual const std::string& GetCharName() const { return m_charName; }
    virtual void SetCharName(const std::string& name) { m_charName = name; }

    // 坐标和方向
    virtual Point GetCurrentPos() const { return m_currentPos; }
    virtual void SetCurrentPos(const Point& pos) { m_currentPos = pos; }
    virtual DirectionType GetDirection() const { return m_direction; }
    virtual void SetDirection(DirectionType dir) { m_direction = dir; }

    // 地图相关
    virtual Environment* GetEnvironment() const { return m_environment; }
    virtual void SetEnvironment(Environment* env) { m_environment = env; }
    virtual const std::string& GetMapName() const { return m_mapName; }
    virtual void SetMapName(const std::string& mapName) { m_mapName = mapName; }

    // 外观属性
    virtual WORD GetAppr() const { return m_appr; }
    virtual void SetAppr(WORD appr) { m_appr = appr; }
    virtual BYTE GetRaceServer() const { return m_raceServer; }
    virtual void SetRaceServer(BYTE race) { m_raceServer = race; }
    virtual BYTE GetRaceImg() const { return m_raceImg; }
    virtual void SetRaceImg(BYTE race) { m_raceImg = race; }

    // 状态属性
    virtual ObjectState GetState() const { return m_state; }
    virtual void SetState(ObjectState state) { m_state = state; }
    virtual bool IsDead() const { return m_state == ObjectState::DEAD; }
    virtual bool IsGhost() const { return m_state == ObjectState::GHOST; }
    virtual bool IsVisible() const { return m_state != ObjectState::HIDE; }

    // 光源
    virtual BYTE GetLight() const { return m_light; }
    virtual void SetLight(BYTE light) { m_light = light; }

    // 名称颜色
    virtual BYTE GetNameColor() const { return m_nameColor; }
    virtual void SetNameColor(BYTE color) { m_nameColor = color; }

    // 生命值相关
    virtual WORD GetHP() const { return m_hp; }
    virtual WORD GetMaxHP() const { return m_maxHP; }
    virtual void SetHP(WORD hp) { m_hp = hp; }
    virtual void SetMaxHP(WORD maxHp) { m_maxHP = maxHp; }
    virtual bool IsAlive() const { return m_hp > 0 && !IsDead(); }

    // 视野范围
    virtual BYTE GetViewRange() const { return m_viewRange; }
    virtual void SetViewRange(BYTE range) { m_viewRange = range; }

    // 虚拟方法
    virtual void Initialize() {}
    virtual void Finalize() {}
    virtual void Run() {}
    virtual bool Walk(DirectionType dir) { return false; }
    virtual bool TurnTo(DirectionType dir);
    virtual void Die();
    virtual void Revive();
    virtual void SendMessage(const std::string& msg, BYTE color = 0) {}
    virtual void SendDefMessage(WORD msgType, WORD recog = 0,
                              WORD param = 0, WORD tag = 0, WORD series = 0) {}

    // 位置和移动相关
    virtual bool CanMove(const Point& target) const;
    virtual bool SpaceMove(const std::string& mapName, int x, int y);
    virtual Point GetFrontPosition() const;
    virtual Point GetNextPosition(DirectionType dir) const;
    virtual void GetAroundPoints(std::vector<Point>& points, int range = 1) const;

    // 视野和可见性
    virtual bool CanSee(const BaseObject* target) const;
    virtual bool IsInViewRange(const Point& pos) const;
    virtual void GetViewObjects(std::vector<BaseObject*>& objects) const;

    // 攻击和战斗相关
    virtual bool IsAttackTarget(const BaseObject* target) const { return false; }
    virtual bool IsProperTarget(const BaseObject* target) const { return false; }
    virtual bool IsProperFriend(const BaseObject* target) const { return false; }
    virtual void BeAttacked(BaseObject* attacker, int damage) {}
    virtual void SetTargetCreature(BaseObject* target) { m_targetCreature = target; }
    virtual BaseObject* GetTargetCreature() const { return m_targetCreature; }

    // 技能相关的目标判断方法（对应delphi的IsProperTargetSKILL_XX系列）
    virtual bool IsProperTargetSKILL_54(const BaseObject* target) const { return false; }
    virtual bool IsProperTargetSKILL_55(int level, const BaseObject* target) const { return false; }
    virtual bool IsProperTargetSKILL_56(const BaseObject* target, int targetX, int targetY) const { return false; }
    virtual bool IsProperTargetSKILL_57(const BaseObject* target) const { return false; }
    virtual bool IsProperTargetSKILL_70(const BaseObject* target) const { return false; }

    // 技能开关控制方法（对应delphi的技能开关）
    virtual void ThrustingOnOff(bool enable) { m_boUseThrusting = enable; }
    virtual void HalfMoonOnOff(bool enable) { m_boUseHalfMoon = enable; }
    virtual void SkillCrsOnOff(bool enable) { m_boCrsHitkill = enable; }
    virtual void Skill42OnOff(bool enable) { m_bo42kill = enable; }
    virtual void Skill43OnOff(bool enable) { m_bo43kill = enable; }

    // 高级战斗方法
    virtual bool RunTo(BYTE dir, bool flag, int destX, int destY) { return false; }
    virtual bool AllowFireHitSkill() const { return m_boFireHitSkill; }
    virtual bool CretInNearXY(const BaseObject* target, int x, int y) const;

    // 伤害计算相关
    virtual int GetHitStruckDamage(BaseObject* attacker, int damage);
    virtual int GetMagStruckDamage(BaseObject* attacker, int damage);
    virtual void StruckDamage(int damage);
    virtual bool _Attack(WORD& hitMode, BaseObject* target);
    virtual int GetAttackPower(int basePower, int powerRange);
    virtual void DamageHealth(int damage);
    virtual void DamageSpell(int spellPoint);
    virtual void DoDamageWeapon(int weaponDamage);

    // 特殊攻击方法
    virtual bool SwordLongAttack(BaseObject* target, int power);
    virtual bool SwordWideAttack(int power);
    virtual bool CrsWideAttack(int power);
    virtual bool DirectAttack(BaseObject* target, int power);

    // 状态和毒药相关
    virtual bool MakePosion(int type, int time, int point);
    virtual void DamageBubbleDefence(int damage);
    virtual void HealthSpellChanged();
    virtual void RecalcAbilitys();

    // 物品掉落和拾取系统
    virtual bool DropItemDown(const UserItem& item, int scatterRange, bool isDieDrop, BaseObject* itemOwner, BaseObject* dropCreator);
    virtual bool DropGoldDown(DWORD gold, bool flag, BaseObject* goldOwner, BaseObject* dropCreator);
    virtual void ScatterGolds(BaseObject* goldCreator);
    virtual void ScatterBagItems(BaseObject* itemCreator);
    virtual void DropUseItems(BaseObject* dropCreator);

    // 状态效果系统
    virtual bool DefenceUp(int seconds);
    virtual bool MagDefenceUp(int seconds);
    virtual bool MagBubbleDefenceUp(int level, int seconds);
    virtual void OpenHolySeizeMode(DWORD interval);
    virtual void BreakHolySeizeMode();
    virtual void OpenCrazyMode(int time);
    virtual void BreakCrazyMode();
    virtual void MakeOpenHealth();
    virtual void BreakOpenHealth();

    // 地图和传送系统
    virtual void SpaceMove(const std::string& mapName, int x, int y, int type = 0);
    virtual bool EnterAnotherMap(Environment* envir, int destX, int destY);
    virtual void MapRandomMove(const std::string& mapName, int range);
    virtual bool GetDropPosition(int orgX, int orgY, int range, int& destX, int& destY);

    // 魔法和技能系统
    virtual void TrainSkill(UserMagic* userMagic, int trainPoint);
    virtual bool CheckMagicLevelup(UserMagic* userMagic);
    virtual bool DoSpell(UserMagic* userMagic, int targetX, int targetY, BaseObject* target);
    virtual int GetSpellPoint(UserMagic* userMagic);
    virtual bool MagCanHitTarget(int x, int y, BaseObject* target);

    // 辅助魔法方法
    virtual bool DoHealSpell(UserMagic* userMagic, BaseObject* target);
    virtual bool DoFireBallSpell(UserMagic* userMagic, int targetX, int targetY, BaseObject* target);
    virtual bool DoLightningSpell(UserMagic* userMagic, BaseObject* target);

    // 消息发送相关（战斗系统专用）
    virtual void SendMsg(BaseObject* obj, WORD msgId, int param1, int param2, int param3, int param4, const std::string& msg) {}
    virtual void SendDelayMsg(BaseObject* obj, WORD msgId, WORD subId, int param1, int param2, int param3, int param4, const std::string& msg, int delay) {}

    // 向周围玩家发送消息（对应delphi的SendRefMsg）
    virtual void SendRefMsg(WORD msgId, WORD param, int param1, int param2, int param3, const std::string& msg = "");

    // 状态查询相关
    virtual int GetCharStatus() const { return m_nCharStatus; }
    virtual bool InSafeZone() const { return false; }
    virtual bool IsGhost() const { return m_state == ObjectState::GHOST; }
    virtual bool WantRefMsg() const { return false; } // 是否想要接收RefMsg（主要用于宠物等）

    // 特征和外观
    virtual DWORD GetFeature() const;
    virtual void SetFeature(DWORD feature);
    virtual void FeatureChanged();
    virtual void StatusChanged();
    virtual BYTE GetCharColor() const;

    // 自定义数据支持
    virtual void AddCustomData(const std::string& key, const std::string& value);
    virtual std::string GetCustomData(const std::string& key) const;

    // 回城相关
    virtual Point GetRecallPos() const { return m_homePos; }
    virtual std::string GetRecallMap() const { return m_homeMap; }
    virtual void SetRecallPos(const Point& pos, const std::string& mapName);

    // 权限检查
    virtual bool IsGM() const { return m_permission >= 10; }
    virtual bool IsAdmin() const { return m_permission >= 6; }
    virtual BYTE GetPermission() const { return m_permission; }
    virtual void SetPermission(BYTE perm) { m_permission = perm; }

    uint32_t GetObjectId() const { return m_objectId; }

    JobType GetJob() const { return m_job; }
    GenderType GetGender() const { return m_gender; }

    bool CanBlock() const { return m_canBlock; }  // 是否阻挡其他对象

    // 新增的属性访问方法
    virtual WORD GetMP() const { return m_mp; }
    virtual WORD GetMaxMP() const { return m_maxMP; }
    virtual void SetMP(WORD mp) { m_mp = mp; }
    virtual void SetMaxMP(WORD maxMp) { m_maxMP = maxMp; }
    virtual void DecMP(WORD amount) { m_mp = (amount >= m_mp) ? 0 : (m_mp - amount); }

    virtual WORD GetLevel() const { return m_level; }
    virtual void SetLevel(WORD level) { m_level = level; }

    virtual WORD GetDC() const { return m_dc; }
    virtual WORD GetMC() const { return m_mc; }
    virtual WORD GetSC() const { return m_sc; }
    virtual WORD GetAC() const { return m_ac; }
    virtual WORD GetMAC() const { return m_mac; }

    virtual void SetDC(WORD dc) { m_dc = dc; }
    virtual void SetMC(WORD mc) { m_mc = mc; }
    virtual void SetSC(WORD sc) { m_sc = sc; }
    virtual void SetAC(WORD ac) { m_ac = ac; }
    virtual void SetMAC(WORD mac) { m_mac = mac; }

    virtual int GetMagicDefense() const { return m_mac; }
    virtual void TakeDamage(BaseObject* attacker, int damage, int damageType = 0) {}
    virtual void Heal(int amount) {
        m_hp = std::min(static_cast<WORD>(m_hp + amount), m_maxHP);
        OnHPChanged();
    }

    // 组队相关
    virtual bool IsInSameGroup(BaseObject* other) const { return false; }

protected:
    // 基础属性
    std::string m_charName;             // 角色名称
    Point m_currentPos{0, 0};           // 当前位置
    DirectionType m_direction = DirectionType::DOWN; // 方向
    Environment* m_environment = nullptr; // 所在环境
    std::string m_mapName;              // 地图名称

    // 外观属性
    WORD m_appr = 0;                    // 外观
    BYTE m_raceServer = 0;              // 服务器种族
    BYTE m_raceImg = 0;                 // 图像种族
    BYTE m_light = 0;                   // 光源
    BYTE m_nameColor = 0;               // 名称颜色
    BYTE m_viewRange = 9;               // 视野范围

    // 状态属性
    ObjectState m_state = ObjectState::NORMAL; // 对象状态
    WORD m_hp = 100;                    // 当前生命值
    WORD m_maxHP = 100;                 // 最大生命值
    WORD m_mp = 100;                    // 当前魔法值
    WORD m_maxMP = 100;                 // 最大魔法值
    WORD m_level = 1;                   // 等级
    BYTE m_permission = 0;              // 权限等级

    // 战斗属性
    WORD m_dc = 0;                      // 物理攻击力
    WORD m_mc = 0;                      // 魔法攻击力
    WORD m_sc = 0;                      // 道术攻击力
    WORD m_ac = 0;                      // 物理防御力
    WORD m_mac = 0;                     // 魔法防御力
    BaseObject* m_targetCreature = nullptr; // 目标对象

    // 回城信息
    Point m_homePos{0, 0};              // 回城位置
    std::string m_homeMap;              // 回城地图

    // 自定义数据
    std::unordered_map<std::string, std::string> m_customData;

    // 时间相关
    DWORD m_lastMoveTime = 0;           // 最后移动时间
    DWORD m_lastAttackTime = 0;         // 最后攻击时间

    // SendRefMsg相关
    DWORD m_sendRefMsgTick = 0;         // 上次发送RefMsg的时间
    std::vector<BaseObject*> m_visibleHumanList; // 可见玩家列表
    bool m_observeMode = false;         // 观察模式
    bool m_fixedHideMode = false;       // 固定隐身模式

    // 状态效果相关
    bool m_boDefenceUp = false;         // 防御提升状态
    bool m_boMagDefenceUp = false;      // 魔防提升状态
    bool m_boMagBubbleDefence = false;  // 魔法盾状态
    BYTE m_btMagBubbleDefenceLevel = 0; // 魔法盾等级
    DWORD m_dwDefenceUpTick = 0;        // 防御提升结束时间
    DWORD m_dwMagDefenceUpTick = 0;     // 魔防提升结束时间
    DWORD m_dwMagBubbleDefenceTick = 0; // 魔法盾结束时间

    // 中毒系统
    struct PoisonInfo {
        int type = 0;           // 毒药类型
        int point = 0;          // 毒性强度
        DWORD endTick = 0;      // 结束时间
    };
    std::vector<PoisonInfo> m_poisonList; // 中毒列表
    DWORD m_dwPoisoningTick = 0;        // 中毒处理时间

    // 物品掉落相关
    std::vector<UserItem> m_dropItemList; // 掉落物品列表
    DWORD m_dwDropItemTick = 0;         // 掉落物品清理时间

    // 特殊状态相关
    bool m_boHolySeize = false;         // 神圣战甲术状态
    bool m_boCrazyMode = false;         // 疯狂模式状态
    bool m_boShowHP = false;            // 显示血量状态
    DWORD m_dwHolySeizeTick = 0;        // 神圣战甲术时间
    DWORD m_dwHolySeizeInterval = 0;    // 神圣战甲术间隔
    DWORD m_dwCrazyModeTick = 0;        // 疯狂模式时间
    DWORD m_dwCrazyModeInterval = 0;    // 疯狂模式间隔
    DWORD m_dwShowHPTick = 0;           // 显示血量时间
    DWORD m_dwShowHPInterval = 0;       // 显示血量间隔

    // 传送相关
    DWORD m_dwTeleportTick = 0;         // 传送时间

    // 战斗系统相关成员变量
    std::string m_sCharName;            // 角色名称
    BYTE m_btRaceServer = 0;            // 服务器种族
    BYTE m_btHitPoint = 10;             // 命中率
    BYTE m_btSpeedPoint = 10;           // 敏捷度
    BYTE m_btAntiPoison = 0;            // 抗毒性
    BYTE m_lifeAttrib = 0;              // 生命属性
    BYTE m_btGreenPoisoningPoint = 0;   // 绿毒点数
    int m_nCharStatus = 0;              // 角色状态
    int m_nHongMoSuite = 0;             // 红魔套装等级

    // 状态时间数组
    WORD m_wStatusTimeArr[MAX_STATUS_ATTRIBUTE] = {0};
    DWORD m_dwStatusArrTick[MAX_STATUS_ATTRIBUTE] = {0};

    // 装备相关
    TUserItem m_useItems[U_MAXUSEITEM];  // 装备数组

    // 属性结构
    TAbility m_Abil;                    // 基础属性
    TAbility m_WAbil;                   // 当前属性（包含装备加成）
    TAddAbility m_AddAbil;              // 附加属性

    // 技能相关
    bool m_boFireHitSkill = false;      // 烈火剑法状态
    bool m_boParalysis = false;         // 麻痹状态
    bool m_boUnParalysis = false;       // 免疫麻痹
    bool m_boAbilMagBubbleDefence = false; // 魔法盾状态
    BYTE m_btMagBubbleDefenceLevel = 0; // 魔法盾等级
    int m_nHitDouble = 0;               // 烈火倍数
    DWORD m_dwLatestFireHitTick = 0;    // 最后烈火时间

    // 简化的技能结构
    struct SimpleSkill {
        BYTE btLevel = 0;
        SimpleSkill() = default;
        SimpleSkill(BYTE level) : btLevel(level) {}
    };

    // 技能指针（简化处理）
    SimpleSkill* m_MagicErgumSkill = nullptr;   // 刺杀剑法
    SimpleSkill* m_MagicBanwolSkill = nullptr;  // 半月弯刀
    SimpleSkill* m_MagicCrsSkill = nullptr;     // 野蛮冲撞

    // 技能开关状态（对应delphi的技能开关变量）
    bool m_boUseThrusting = false;      // 是否使用刺杀剑法
    bool m_boUseHalfMoon = false;       // 是否使用半月弯刀
    bool m_boFireHitSkill = false;      // 是否使用烈火剑法
    bool m_boCrsHitkill = false;        // 是否使用野蛮冲撞
    bool m_bo42kill = false;            // 技能42开关
    bool m_bo43kill = false;            // 技能43开关
    DWORD m_dwLatestFireHitTick = 0;    // 最后使用烈火剑法时间

    // 内部方法
    virtual void OnPositionChanged() {}
    virtual void OnDirectionChanged() {}
    virtual void OnStateChanged() {}
    virtual void OnHPChanged() {}

    uint32_t m_objectId;
    JobType m_job;
    GenderType m_gender;
    bool m_isDead;
    bool m_visible;
    bool m_canBlock;
};

// 智能指针类型定义
using BaseObjectPtr = std::shared_ptr<BaseObject>;
using BaseObjectWeakPtr = std::weak_ptr<BaseObject>;

} // namespace MirServer